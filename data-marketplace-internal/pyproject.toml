# SPDX-FileCopyrightText: Terrafloww Labs, 2025

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "data-marketplace"
dynamic = ["version"]
description = "Scalable data marketplace with Delta Lake storage and DuckDB querying for STAC collections"
readme = "README.md"
license = {text = "Terrafloww Labs Proprietary"}
requires-python = ">=3.9"
authors = [
    { name = "Terrafloww Labs", email = "<EMAIL>" },
]
keywords = [
    "geospatial",
    "stac", 
    "delta-lake",
    "duckdb",
    "geoparquet",
    "s3",
    "spatial-indexing"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: GIS",
    "Topic :: Database",
]

dependencies = [
    # Core dependencies - Updated to latest versions (July 2025)
    "deltalake>=1.1.3", # Latest with MERGE/UPDATE/DELETE support
    "duckdb>=1.3.0", # Latest with external file cache
    "pyarrow>=17.0.0", # Latest with enhanced Bloom filter support
    "pandas>=2.2.0",
    # Spatial and geospatial - Updated for GeoArrow support
    "s2sphere>=0.2.5",
    "shapely>=2.0.0",
    "geopandas>=1.0.0", # Latest with improved Arrow integration
    "geoarrow-pyarrow>=0.2.0", # GeoArrow support for proper geometry encoding
    "stac-geoparquet>=0.6.0",
    # API and web
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "httpx>=0.25.0",
    # Cloud and storage
    "boto3>=1.34.0",
    "s3fs>=2023.12.0",
    # STAC and metadata
    "pystac>=1.8.0",
    "pystac-client>=0.7.0",
    # Utilities
    "click>=8.1.0",
    "rich>=13.0.0",
    "tqdm>=4.66.0",
    "python-dotenv>=1.0.0",
    "gdal==3.8.4",
    # COG parsing dependencies
    "aiohttp>=3.9.0",
    "cachetools>=5.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
    "mkdocstrings[python]>=0.23.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0", 
    "pytest-cov>=4.1.0",
    "moto[s3]>=4.2.0",  # For S3 mocking
]

[project.urls]
Homepage = "https://github.com/terrafloww/data-marketplace-internal"
Documentation = "https://github.com/terrafloww/data-marketplace-internal/docs"
Repository = "https://github.com/terrafloww/data-marketplace-internal"
Issues = "https://github.com/terrafloww/data-marketplace-internal/issues"

[project.scripts]
data-marketplace = "data_marketplace.cli:main"

[tool.hatch.version]
path = "src/data_marketplace/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["src/data_marketplace"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["data_marketplace"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "deltalake.*",
    "duckdb.*",
    "s2sphere.*",
    "stac_geoparquet.*",
    "geoarrow.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "asyncio: marks tests as using asyncio/pytest-asyncio",
]

[tool.pytest_asyncio]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flake8>=7.3.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
]
