# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Master index table management for efficient scene discovery."""

import logging
from typing import Optional, List, Dict, Any
import pyarrow as pa
from data_marketplace.config.settings import Settings
from data_marketplace.storage.delta_manager import DeltaManager
from data_marketplace.spatial.bbox_utils import BboxUtils

logger = logging.getLogger(__name__)


class MasterIndexManager:
    """Manager for the master index table used for efficient scene discovery."""
    
    def __init__(self, settings: Optional[Settings] = None):
        """
        Initialize master index manager.
        
        Args:
            settings: Settings instance (uses global if None)
        """
        if settings is None:
            from data_marketplace.config.settings import settings as global_settings
            settings = global_settings
        
        self.settings = settings
        self.delta_manager = DeltaManager(settings)
        self.bbox_utils = BboxUtils()
        self.logger = logger
    
    @staticmethod
    def get_master_index_schema() -> pa.Schema:
        """
        Get the schema for the master index table.
        
        Returns:
            PyArrow schema for master index
        """
        return pa.schema([
            ("scene_id", pa.string()),
            ("collection", pa.string()),
            ("datetime", pa.timestamp("us")),
            ("year", pa.int32()),
            ("month", pa.int32()),
            ("s2_cell_id", pa.uint64()),
            ("bbox", BboxUtils.get_bbox_schema()),
            ("geometry", pa.binary()),  # WKB format
            ("table_path", pa.string()),
            ("partition_path", pa.string()),
            ("file_size", pa.int64()),
            ("created_at", pa.timestamp("us")),
        ])
    
    def create_master_index(self) -> bool:
        """
        Create the master index table if it doesn't exist.
        
        Returns:
            True if successful
        """
        table_name = self.settings.delta.master_index_table
        
        if self.delta_manager.table_exists(table_name):
            self.logger.info(f"Master index table {table_name} already exists")
            return True
        
        schema = self.get_master_index_schema()
        partition_by = ["year", "month"]
        
        success = self.delta_manager.create_table(
            table_name=table_name,
            schema=schema,
            partition_by=partition_by
        )
        
        if success:
            self.logger.info(f"Created master index table: {table_name}")
        
        return success
    
    def add_scenes(self, scene_records: List[Dict[str, Any]]) -> bool:
        """
        Add scene records to the master index.
        
        Args:
            scene_records: List of scene record dictionaries
            
        Returns:
            True if successful
        """
        if not scene_records:
            return True
        
        try:
            # Convert to PyArrow table
            table = pa.table(scene_records, schema=self.get_master_index_schema())
            
            # Write to master index
            table_name = self.settings.delta.master_index_table
            success = self.delta_manager.write_table(
                table_name=table_name,
                data=table,
                mode="append"
            )
            
            if success:
                self.logger.info(f"Added {len(scene_records)} scenes to master index")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to add scenes to master index: {e}")
            return False
    
    def get_master_index_table(self):
        """Get the master index Delta table."""
        table_name = self.settings.delta.master_index_table
        return self.delta_manager.read_table(table_name)
    
    def query_scenes_by_bbox(
        self, 
        bbox: tuple, 
        collections: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Optional[pa.Table]:
        """
        Query scenes by bounding box and optional filters.
        
        Args:
            bbox: Bounding box as (minx, miny, maxx, maxy)
            collections: List of collection names to filter by
            start_date: Start date filter (ISO format)
            end_date: End date filter (ISO format)
            
        Returns:
            PyArrow table with matching scenes or None
        """
        try:
            table = self.get_master_index_table()
            if table is None:
                return None
            
            # Convert to PyArrow dataset for querying
            dataset = table.to_pyarrow_dataset()
            
            # Build filter conditions
            filters = []
            
            # Spatial filter using bbox intersection
            minx, miny, maxx, maxy = bbox
            filters.extend([
                ("bbox.xmax", ">=", minx),
                ("bbox.xmin", "<=", maxx),
                ("bbox.ymax", ">=", miny),
                ("bbox.ymin", "<=", maxy),
            ])
            
            # Collection filter
            if collections:
                filters.append(("collection", "in", collections))
            
            # Date filters
            if start_date:
                filters.append(("datetime", ">=", start_date))
            if end_date:
                filters.append(("datetime", "<=", end_date))
            
            # Execute query
            result = dataset.to_table(filter=filters)
            
            self.logger.info(f"Found {len(result)} scenes matching query")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to query scenes: {e}")
            return None
    
    def get_collection_stats(self, collection: str) -> Optional[Dict[str, Any]]:
        """
        Get statistics for a collection.
        
        Args:
            collection: Collection name
            
        Returns:
            Dictionary with collection statistics
        """
        try:
            table = self.get_master_index_table()
            if table is None:
                return None
            
            # Filter by collection
            dataset = table.to_pyarrow_dataset()
            collection_table = dataset.to_table(filter=[("collection", "==", collection)])
            
            if len(collection_table) == 0:
                return None
            
            # Calculate statistics
            stats = {
                "scene_count": len(collection_table),
                "total_size": collection_table["file_size"].sum().as_py(),
                "date_range": {
                    "start": collection_table["datetime"].min().as_py(),
                    "end": collection_table["datetime"].max().as_py(),
                },
                "spatial_extent": self._calculate_spatial_extent(collection_table),
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get collection stats: {e}")
            return None
    
    def _calculate_spatial_extent(self, table: pa.Table) -> Dict[str, float]:
        """Calculate spatial extent from bbox column."""
        try:
            bbox_column = table["bbox"]
            
            # Extract min/max values from bbox structs
            xmin_values = pa.compute.struct_field(bbox_column, [0])  # xmin
            ymin_values = pa.compute.struct_field(bbox_column, [1])  # ymin
            xmax_values = pa.compute.struct_field(bbox_column, [2])  # xmax
            ymax_values = pa.compute.struct_field(bbox_column, [3])  # ymax
            
            return {
                "xmin": pa.compute.min(xmin_values).as_py(),
                "ymin": pa.compute.min(ymin_values).as_py(),
                "xmax": pa.compute.max(xmax_values).as_py(),
                "ymax": pa.compute.max(ymax_values).as_py(),
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate spatial extent: {e}")
            return {"xmin": 0, "ymin": 0, "xmax": 0, "ymax": 0}
