# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Command-line interface for data marketplace operations."""

import click
import asyncio
import logging
from pathlib import Path
from typing import Optional

from data_marketplace.config.settings import Settings
from data_marketplace.storage.s3_config import S3Config


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.option('--config', type=click.Path(exists=True), help='Configuration file path')
@click.pass_context
def main(ctx, debug: bool, config: Optional[str]):
    """Data Marketplace CLI - Manage STAC collections with Delta Lake and S3."""
    # Set up logging
    log_level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize settings
    if config:
        # TODO: Load from config file
        settings = Settings()
    else:
        settings = Settings()
    
    ctx.ensure_object(dict)
    ctx.obj['settings'] = settings


@main.command()
@click.pass_context
def init(ctx):
    """Initialize S3 bucket and basic configuration."""
    settings = ctx.obj['settings']
    s3_config = S3Config(settings)
    
    click.echo(f"Initializing data marketplace...")
    click.echo(f"S3 Bucket: {settings.s3.bucket_name}")
    click.echo(f"Region: {settings.s3.region}")
    
    # Check/create bucket
    if s3_config.bucket_exists():
        click.echo("✓ S3 bucket exists")
    else:
        click.echo("Creating S3 bucket...")
        if s3_config.create_bucket():
            click.echo("✓ S3 bucket created")
        else:
            click.echo("✗ Failed to create S3 bucket")
            return
    
    # Create workspace directory
    settings.workspace_dir.mkdir(parents=True, exist_ok=True)
    click.echo(f"✓ Workspace directory: {settings.workspace_dir}")
    
    click.echo("Initialization complete!")


@main.command()
@click.pass_context
def status(ctx):
    """Show system status and configuration."""
    settings = ctx.obj['settings']
    s3_config = S3Config(settings)
    
    click.echo("Data Marketplace Status")
    click.echo("=" * 30)
    
    # S3 Status
    click.echo(f"S3 Bucket: {settings.s3.bucket_name}")
    if s3_config.bucket_exists():
        click.echo("  Status: ✓ Available")
        
        # List tables
        tables = s3_config.list_tables()
        click.echo(f"  Tables: {len(tables)}")
        for table in tables[:5]:  # Show first 5
            size = s3_config.get_table_size(table)
            click.echo(f"    - {table} ({size:,} bytes)")
        if len(tables) > 5:
            click.echo(f"    ... and {len(tables) - 5} more")
    else:
        click.echo("  Status: ✗ Not found")
    
    # Configuration
    click.echo(f"\nConfiguration:")
    click.echo(f"  Region: {settings.s3.region}")
    click.echo(f"  Table Root: {settings.delta.table_root}")
    click.echo(f"  S2 Cell Level: {settings.spatial.s2_cell_level}")
    click.echo(f"  Workspace: {settings.workspace_dir}")


@main.command()
@click.argument('collection_name')
@click.option('--bbox', nargs=4, type=float, help='Bounding box: minx miny maxx maxy')
@click.option('--start-date', help='Start date (YYYY-MM-DD)')
@click.option('--end-date', help='End date (YYYY-MM-DD)')
@click.option('--data-source', default='sentinel-2-l2a', help='STAC data source')
@click.pass_context
def index(ctx, collection_name: str, bbox: tuple, start_date: str, end_date: str, data_source: str):
    """Index a STAC collection to Delta Lake."""
    settings = ctx.obj['settings']
    
    click.echo(f"Indexing collection: {collection_name}")
    click.echo(f"Data source: {data_source}")
    
    if bbox:
        click.echo(f"Bounding box: {bbox}")
    if start_date and end_date:
        click.echo(f"Date range: {start_date} to {end_date}")
    
    # TODO: Implement actual indexing
    click.echo("Note: Indexing implementation coming in next phase")


@main.command()
@click.option('--host', default='0.0.0.0', help='API server host')
@click.option('--port', default=8000, help='API server port')
@click.option('--reload', is_flag=True, help='Enable auto-reload for development')
@click.pass_context
def serve(ctx, host: str, port: int, reload: bool):
    """Start the API server."""
    settings = ctx.obj['settings']
    
    click.echo(f"Starting API server on {host}:{port}")
    
    # TODO: Implement actual server startup
    click.echo("Note: API server implementation coming in next phase")


@main.command()
@click.pass_context
def test_spatial(ctx):
    """Test spatial indexing utilities."""
    from data_marketplace.spatial.s2_utils import S2Utils
    from data_marketplace.spatial.bbox_utils import BboxUtils, BboxStruct
    
    click.echo("Testing spatial utilities...")
    
    # Test S2 utilities
    s2_utils = S2Utils(cell_level=6)
    
    # San Francisco point
    lon, lat = -122.4194, 37.7749
    cell_id = s2_utils.point_to_s2_cell(lon, lat)
    click.echo(f"Point ({lon}, {lat}) -> S2 cell: {cell_id}")
    
    # Convert back to polygon
    polygon = s2_utils.s2_cell_to_polygon(cell_id)
    click.echo(f"S2 cell bounds: {polygon.bounds}")
    
    # Test bbox utilities
    bbox_utils = BboxUtils()
    bbox = BboxStruct(xmin=-122.5, ymin=37.7, xmax=-122.3, ymax=37.8)
    
    click.echo(f"Bbox: {bbox.to_dict()}")
    click.echo(f"Area: {bbox.area():.6f} square degrees")
    
    # Get S2 cells for bbox
    cells = s2_utils.bbox_to_s2_cells(bbox.to_tuple())
    click.echo(f"Bbox covered by {len(cells)} S2 cells")
    
    click.echo("✓ Spatial utilities working correctly")


if __name__ == '__main__':
    main()
