# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Delta Lake STAC Ingester with optimized Parquet settings and real COG header parsing.

This module implements a production-ready STAC ingestion pipeline that:
- Uses delta-rs for ACID transactions and schema evolution
- Integrates existing aiohttp COG header parsing (no major changes)
- Applies advanced Parquet optimization for repeated data compression
- Implements year/month temporal partitioning
- Supports spatial indexing with S2 cells and GeoArrow encoding
"""

import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

import pyarrow as pa
import pyarrow.compute as pc
from deltalake import DeltaTable, WriterProperties, ColumnProperties, BloomFilterProperties
import pystac_client

# Import our optimized schemas and settings
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema, StacSchemaConfig
from data_marketplace.ingestion.restart_manager import RestartManager
from data_marketplace.ingestion.stac_processor import StacProcessor
from data_marketplace.cog.stac_cog_processor import StacCogProcessor
from data_marketplace.storage.delta_manager import DeltaManager
from data_marketplace.config.parquet_optimization import (
    get_optimized_stac_settings,
    get_delta_lake_properties
)




logger = logging.getLogger(__name__)


class DeltaStacIngester:
    """
    Production-ready unified STAC + COG ingester using Delta Lake.

    Features:
    - Single unified table (one row per COG asset with repeated scene metadata)
    - Real STAC data ingestion with existing aiohttp COG parsing
    - Delta Lake ACID transactions with delta-rs
    - Advanced Parquet optimization for repeated data compression
    - Year/month temporal partitioning
    - S2 spatial indexing with native GeoArrow encoding
    - Optimized for cross-collection spatial + technical queries
    - Scalable from 10k to 100M+ records
    """

    # Lightweight memory diagnostics without external deps
    @staticmethod
    def _log_memory(context: str = "") -> None:
        try:
            import os, gc
            rss_kb = None
            # Try resource on Unix
            try:
                import resource
                usage = resource.getrusage(resource.RUSAGE_SELF)
                # ru_maxrss is kilobytes on Linux, bytes on macOS; normalize to MB
                ru = usage.ru_maxrss
                mb = ru / 1024.0 if ru > 1024 * 1024 else ru / 1024.0
                rss_kb = ru if ru > 1024 else ru * 1024
                logger.debug(f"[mem] RSS max ~{mb:.1f} MB (ru_maxrss), gc objects: gen0={len(gc.get_objects())}")
            except Exception:
                pass
            # /proc/self/statm (Linux)
            if os.path.exists('/proc/self/statm'):
                with open('/proc/self/statm') as f:
                    parts = f.read().split()
                    if len(parts) >= 2:
                        pages = int(parts[1])
                        page_size = os.sysconf('SC_PAGE_SIZE')
                        rss_mb = pages * page_size / (1024 * 1024)
                        logger.info(f"[mem] {context} RSS ~{rss_mb:.1f} MB")
        except Exception:
            pass


    def __init__(
        self,
        unified_table_path: str,
        config: Optional[StacSchemaConfig] = None,
        storage_options: Optional[Dict[str, str]] = None
    ):
        """
        Initialize the unified Delta STAC ingester.

        Args:
            unified_table_path: Path to Delta Lake unified STAC + COG table
            config: Schema configuration
            storage_options: S3/cloud storage options for Delta Lake
        """
        self.unified_table_path = unified_table_path
        self.config = config or StacSchemaConfig()
        self.storage_options = storage_options or {}

        # Initialize unified schema
        self.unified_schema = UnifiedStacSchema(self.config)

        # Initialize modular components
        self.restart_manager = RestartManager(unified_table_path, storage_options)
        self.cog_processor = StacCogProcessor()
        # existing_key_checker will be bound after optional prefetch; default to no-op
        self.stac_processor = StacProcessor(
            self.unified_schema,
            self.cog_processor,
            existing_key_checker=None,
        )
        self.delta_manager = DeltaManager()

        # Get optimization settings for unified table
        self.parquet_settings = get_optimized_stac_settings()  # Use STAC settings as base
        self.delta_properties = get_delta_lake_properties()

        # Update dictionary encoding for unified schema
        self.parquet_settings['use_dictionary'] = self.unified_schema.get_dictionary_columns()

        logger.info(f"Initialized Unified Delta STAC Ingester")
        logger.info(f"Unified table: {unified_table_path}")
        logger.info(f"Dictionary encoding enabled for {len(self.parquet_settings['use_dictionary'])} columns")
        logger.info(f"Bloom filters enabled for {len(self.unified_schema.get_bloom_filter_columns())} columns")
    
    async def ingest_stac_collection(
        self,
        stac_api_url: str,
        collection_id: str,
        max_items: Optional[int] = None,
        datetime_range: Optional[str] = None,
        bbox: Optional[List[float]] = None,
        batch_size: int = 2000,
        max_concurrent_cog_requests: int = 200,
        max_concurrent_stac_items: int = 20,
        optimize_every_n_batches: int = 100
    ) -> Dict[str, Any]:
        """
        Ingest STAC collection into unified table with real data and COG header parsing.

        Args:
            stac_api_url: STAC API endpoint URL
            collection_id: Collection to ingest (e.g., 'sentinel-2-l2a')
            max_items: Maximum items to ingest (for testing)
            datetime_range: Date range filter (e.g., '2023-01-01/2023-12-31')
            bbox: Bounding box filter [west, south, east, north]
            batch_size: Items per batch for processing (default: 2000)
            max_concurrent_cog_requests: Max concurrent COG header requests (default: 200)
            max_concurrent_stac_items: Max concurrent STAC items processed in parallel (default: 20)
            optimize_every_n_batches: Run optimization every N batches (default: 100)

        Returns:
            Ingestion statistics and performance metrics
        """
        start_time = datetime.now()
        stats = {
            "collection_id": collection_id,
            "start_time": start_time.isoformat(),
            "stac_items_processed": 0,
            "cog_assets_processed": 0,
            "unified_records_written": 0,  # One record per COG asset
            "errors": [],
            "performance": {}
        }
        
        logger.info(f"Starting STAC ingestion for collection: {collection_id}")
        logger.info(f"Max items: {max_items}, Batch size: {batch_size}")
        
        try:
            # Connect to STAC API
            catalog = pystac_client.Client.open(stac_api_url)

            # Smart COG key completeness check - always run when datetime_range is provided
            if datetime_range:
                logger.info(f"Checking COG key completeness for date range: {datetime_range}")

                # Use the new simplified COG key completeness check
                needs_ingestion = self.restart_manager.check_missing_cog_keys_for_range(
                    catalog, collection_id, datetime_range, bbox
                )

                if not needs_ingestion:
                    logger.info(f"✅ All COG keys already exist for date range {datetime_range} - skipping ingestion")
                    # Return empty stats since no work was needed
                    stats["stac_items_processed"] = 0
                    stats["unified_records_written"] = 0
                    stats["message"] = "All COG keys already exist - no ingestion needed"
                    return stats
                else:
                    logger.info(f"🔍 Missing COG keys found for range {datetime_range} - proceeding with ingestion")

            # Build search parameters
            search_params = {"collections": [collection_id]}
            if datetime_range:
                search_params["datetime"] = datetime_range
            if bbox:
                search_params["bbox"] = bbox
            if max_items:
                search_params["max_items"] = max_items

            # Prefetch existing (scene_id -> cog_key set) once by default when a datetime_range is provided
            # Check if prefetch was already done during pre-check to avoid double work
            if datetime_range:
                try:
                    if hasattr(self.restart_manager, '_prefetch_map') and self.restart_manager._prefetch_map:
                        # Reuse existing prefetch from pre-check
                        pre_map = self.restart_manager._prefetch_map
                        logger.info(f"Reusing existing keys prefetch for range {datetime_range}: {len(pre_map)} scenes with prior rows")
                    else:
                        # Do fresh prefetch if not already done
                        pre_map = self.restart_manager.prefetch_existing_cog_keys_for_range(datetime_range, bbox)
                        logger.info(f"Existing keys prefetch enabled for range {datetime_range}: {len(pre_map)} scenes with prior rows")

                    # Bind a dict-backed checker into the processor for O(1) lookups
                    self.stac_processor.existing_key_checker = lambda sid, _: pre_map.get(sid, set())
                except Exception as e:
                    logger.warning(f"Prefetch failed; proceeding without existing-key filtering: {e}")
                    self.stac_processor.existing_key_checker = None

            logger.info(f"STAC search parameters: {search_params}")

            # Search for items
            search = catalog.search(**search_params)

            # Memory baseline
            self._log_memory("before_batches")

            # Process items in batches
            batch_count = 0
            async for batch_stats, unified_records in self.stac_processor.process_stac_items_in_batches(
                search.items(),
                batch_size,
                max_concurrent_cog_requests,
                max_concurrent_stac_items
            ):
                    # Write unified records using DeltaManager
                    if unified_records:
                        try:
                            unified_table = self.unified_schema.create_pyarrow_table(unified_records)
                            records_written = await self._write_unified_records_with_delta_manager(unified_table)
                            batch_stats["unified_records_written"] = records_written
                        except Exception as e:
                            logger.error(f"❌ Error creating PyArrow table: {e}")
                            batch_stats["unified_records_written"] = 0
                        finally:
                            # Proactively free large local references to help GC between batches
                            try:
                                del unified_records
                                del unified_table
                            except Exception:
                                pass

                    # Update statistics
                    stats["stac_items_processed"] += batch_stats["stac_items_processed"]
                    stats["cog_assets_processed"] += batch_stats.get("cog_assets_processed", 0)
                    stats["unified_records_written"] += batch_stats["unified_records_written"]
                    stats["errors"].extend(batch_stats["errors"])

                    # Simple progress logging
                    batch_count += 1
                    logger.info(
                        f"✅ Batch {batch_count} complete: {batch_stats['stac_items_processed']} items → "
                        f"{batch_stats['unified_records_written']} records written "
                        f"(Total: {stats['stac_items_processed']} items, {stats['unified_records_written']} records)"
                    )

                    # Log memory after each batch and free references
                    self._log_memory(f"after_batch_{batch_count}")

                    # Optimize every N batches
                    if batch_count % optimize_every_n_batches == 0:
                        logger.info(f"🔧 Running optimization after {batch_count} batches...")
                        # Best effort compact; ignore failures to avoid halting ingestion
                        try:
                            self.delta_manager.optimize_table("unified_stac_table")
                        except Exception as opt_e:
                            logger.debug(f"Optimize skipped: {opt_e}")

                    # Periodically clear small caches to avoid unbounded growth
                    if batch_count % 10 == 0:
                        try:
                            self.stac_processor.clear_existing_keys_cache()
                        except Exception:
                            pass

                    # Break if we've hit max_items
                    if max_items and stats["stac_items_processed"] >= max_items:
                        break
        
        except Exception as e:
            logger.error(f"Error during STAC ingestion: {e}", exc_info=True)
            stats["errors"].append({"type": "ingestion_error", "message": str(e)})
        
        # Calculate final statistics
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        stats.update({
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "performance": {
                "items_per_second": stats["stac_items_processed"] / duration if duration > 0 else 0,
                "cog_assets_per_second": stats["cog_assets_processed"] / duration if duration > 0 else 0,
                "unified_records_per_second": stats["unified_records_written"] / duration if duration > 0 else 0,
                "total_records_written": stats["unified_records_written"]
            }
        })

        logger.info(f"Unified STAC ingestion completed in {duration:.2f}s")
        logger.info(f"Performance: {stats['performance']['items_per_second']:.2f} items/sec")
        logger.info(f"Total unified records written: {stats['performance']['total_records_written']}")

        # Final optimization
        logger.info("Final optimization...")
        try:
            self.delta_manager.optimize_table_uri(self.unified_table_path, storage_options=self.storage_options)
        except Exception as e:
            logger.debug(f"Final optimize skipped: {e}")

        return stats

    # Optimization method moved to DeltaManager


    










    async def _write_unified_records_with_delta_manager(self, unified_table: pa.Table) -> int:
        """Write unified records using DeltaManager for MERGE deduplication."""
        try:
            logger.info(f"🔄 Starting Delta write to: {self.unified_table_path}")
            logger.info(f"📊 Table info: {len(unified_table)} rows, {len(unified_table.columns)} columns")
            logger.info(f"🔑 Storage options: {self.storage_options}")

            # Create writer properties
            # Configure Bloom filters only for selected columns (avoid enabling for all columns)
            bloom_cols = list(self.unified_schema.get_bloom_filter_columns()) if hasattr(self.unified_schema, 'get_bloom_filter_columns') else []
            col_props = None
            if bloom_cols:
                col_props = {
                    col: ColumnProperties(
                        bloom_filter_properties=BloomFilterProperties(True, 0.1, 1000)
                    ) for col in bloom_cols
                }

            writer_properties = WriterProperties(
                data_page_size_limit=self.parquet_settings["data_page_size"],
                max_row_group_size=self.parquet_settings["row_group_size"],
                column_properties=col_props
            )

            # Use explicit output path URI so S3 writes go where the user requested
            logger.info(f"🚀 Calling DeltaManager.write_with_merge_deduplication_uri...")
            self.delta_manager.write_with_merge_deduplication_uri(
                table_uri=self.unified_table_path,
                data=unified_table,
                merge_key=["scene_id", "cog_key"],  # Use composite key for proper deduplication
                partition_by=["year", "month"],
                writer_properties=writer_properties,
                storage_options=self.storage_options,
            )

            logger.info(f"💾 Delta write complete: {len(unified_table)} records written to {self.unified_table_path}")
            return len(unified_table)

        except Exception as e:
            logger.error(f"Error writing unified records with DeltaManager: {e}", exc_info=True)
            return 0

    def get_table_info(self) -> Dict[str, Any]:
        """Get information about the unified Delta Lake table."""
        info = {
            "unified_table": {"path": self.unified_table_path, "exists": False, "version": None, "num_files": 0}
        }

        try:
            # Check unified table
            if Path(self.unified_table_path).exists():
                unified_dt = DeltaTable(self.unified_table_path, storage_options=self.storage_options)
                info["unified_table"].update({
                    "exists": True,
                    "version": unified_dt.version(),
                    "num_files": len(unified_dt.files()),
                    "schema": str(unified_dt.schema())
                })
        except Exception as e:
            logger.debug(f"Could not read unified table info: {e}")

        return info

    async def test_with_sample_data(
        self,
        stac_api_url: str = "https://earth-search.aws.element84.com/v1",
        collection_id: str = "sentinel-2-l2a",
        max_items: int = 10,
        bbox: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """
        Test ingestion with a small sample of real STAC data.

        Args:
            stac_api_url: STAC API endpoint
            collection_id: Collection to test with
            max_items: Number of items to test (start small)
            bbox: Optional bounding box for spatial filtering

        Returns:
            Test results and performance metrics
        """
        logger.info(f"Testing Delta STAC Ingester with {max_items} items from {collection_id}")

        # Use a recent date range for testing
        datetime_range = "2024-01-01/2024-01-31"

        # Default to a small area in Europe if no bbox provided
        if bbox is None:
            bbox = [2.0, 46.0, 3.0, 47.0]  # Small area in France

        # Run ingestion test
        results = await self.ingest_stac_collection(
            stac_api_url=stac_api_url,
            collection_id=collection_id,
            max_items=max_items,
            datetime_range=datetime_range,
            bbox=bbox,
            batch_size=min(max_items, 100),  # Small batches for testing
            max_concurrent_cog_requests=5    # Conservative for testing
        )

        # Get table information
        table_info = self.get_table_info()
        results["table_info"] = table_info

        return results




