# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
STAC item processing and batch management.

This module handles the processing of STAC items in batches,
including parallel processing and unified record creation.
"""

import asyncio
import gc
import logging
from typing import List, Dict, Any, Iterator, AsyncGenerator, Tuple
from pystac import Item

logger = logging.getLogger(__name__)


class StacProcessor:
    """Handles STAC item processing and batch management."""

    def __init__(self, unified_schema, cog_processor, existing_key_checker=None):
        """
        Initialize STAC processor.

        Args:
            unified_schema: UnifiedStacSchema instance
            cog_processor: COG processor instance
            existing_key_checker: Optional callable(scene_id:str, collection:str|None) -> set[str]
        """
        self.unified_schema = unified_schema
        self.cog_processor = cog_processor
        self.existing_key_checker = existing_key_checker
        # In-memory per-run cache for existing cog keys to avoid repeated table scans
        self._existing_keys_cache = {}  # type: dict[tuple[str,str|None], set]

    def clear_existing_keys_cache(self) -> None:
        """Clear per-run existing-keys cache to avoid unbounded growth across large runs."""
        try:
            self._existing_keys_cache.clear()
        except Exception:
            self._existing_keys_cache = {}

    async def process_stac_items_in_batches(
        self,
        stac_items: Iterator[Item],
        batch_size: int,
        max_concurrent_cog_requests: int,
        max_concurrent_stac_items: int = 20,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process STAC items in batches with controlled concurrency.

        Args:
            stac_items: Iterator of STAC items
            batch_size: Number of items per batch
            max_concurrent_cog_requests: Max concurrent COG requests per item
            max_concurrent_stac_items: Max concurrent STAC items per batch

        Yields:
            Batch statistics dictionaries
        """
        batch = []
        batch_count = 0

        for item in stac_items:
            batch.append(item)

            if len(batch) >= batch_size:
                batch_stats, unified_records = await self.process_stac_batch(
                    batch,
                    max_concurrent_cog_requests,
                    max_concurrent_stac_items,
                )
                yield batch_stats, unified_records

                # Clear batch and force garbage collection to prevent memory leaks
                batch.clear()
                batch_count += 1

                # Clear existing keys cache periodically to prevent unbounded growth
                if batch_count % 10 == 0:  # Every 10 batches
                    self.clear_existing_keys_cache()
                    gc.collect()  # Force garbage collection
                    logger.debug(f"Cleared existing keys cache and ran GC after {batch_count} batches")

        # Process remaining items
        if batch:
            batch_stats, unified_records = await self.process_stac_batch(
                batch,
                max_concurrent_cog_requests,
                max_concurrent_stac_items,
            )
            yield batch_stats, unified_records
            batch.clear()

    async def process_stac_batch(
        self,
        stac_items: List[Item],
        max_concurrent_cog_requests: int,
        max_concurrent_stac_items: int = 20,
    ) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Process a batch of STAC items in parallel.

        Args:
            stac_items: List of STAC items to process
            max_concurrent_cog_requests: Max concurrent COG requests per item
            max_concurrent_stac_items: Max concurrent STAC items

        Returns:
            Tuple of (batch processing statistics, list of unified records)
        """
        batch_stats = {
            "stac_items_processed": 0,
            "cog_assets_processed": 0,
            "unified_records_created": 0,
            "unified_records_written": 0,
            "errors": [],
        }

        # Process STAC items in parallel with semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent_stac_items)

        async def process_single_stac_item(
            item: Item,
        ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
            """Process a single STAC item and return (unified_records, errors)."""
            async with semaphore:
                try:
                    # Create unified records for this STAC item (one record per COG asset)
                    item_unified_records = await self.create_unified_records_for_item(
                        item, max_concurrent_cog_requests
                    )
                    return item_unified_records, []
                except Exception as e:
                    error_info = {
                        "item_id": item.id,
                        "error": str(e),
                        "type": "stac_processing_error",
                    }
                    logger.error(f"Error processing STAC item {item.id}: {e}")
                    return [], [error_info]

        # Process all items in parallel
        tasks = [process_single_stac_item(item) for item in stac_items]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Collect results
        unified_records = []
        processed_items = 0
        total_cog_assets = 0
        for result in results:
            if isinstance(result, asyncio.CancelledError):
                raise result
            if isinstance(result, Exception):
                batch_stats["errors"].append(
                    {"error": str(result), "type": "task_exception"}
                )
            else:
                item_records, item_errors = result
                if item_records:
                    processed_items += 1
                    total_cog_assets += len(item_records)  # Each record represents one COG asset
                unified_records.extend(item_records)
                batch_stats["errors"].extend(item_errors)

        batch_stats["stac_items_processed"] = processed_items
        batch_stats["cog_assets_processed"] = total_cog_assets
        batch_stats["unified_records_created"] = len(unified_records)

        return batch_stats, unified_records

    async def create_unified_records_for_item(
        self,
        stac_item: Item,
        max_concurrent_requests: int,
    ) -> List[Dict[str, Any]]:
        """
        Create unified records for a STAC item (one record per COG asset).

        Args:
            stac_item: STAC item to process
            max_concurrent_requests: Max concurrent COG requests

        Returns:
            List of unified records (one per COG asset)
        """
        unified_records = []

        try:
            # If we have a checker, compute which COG keys are already present and only process missing keys
            only_keys = None
            try:
                if self.existing_key_checker is not None:
                    scene_id = getattr(stac_item, 'id', None)
                    collection = getattr(stac_item, 'collection_id', None)
                    # pystac.Item has .collection_id attribute
                    only_keys = None
                    if scene_id:
                        cache_key = (scene_id, collection)
                        if cache_key in self._existing_keys_cache:
                            existing = self._existing_keys_cache[cache_key]
                        else:
                            existing = self.existing_key_checker(scene_id, collection)
                            self._existing_keys_cache[cache_key] = set(existing or set())
                        if existing is not None:
                            # Determine candidate COG keys from item assets (filter to COG assets only)
                            candidate_keys = {
                                key for key, asset in getattr(stac_item, 'assets', {}).items()
                                if self.cog_processor.is_cog_asset(asset)
                            }
                            # Compute missing among COG candidates only
                            missing = candidate_keys - set(existing)
                            if missing:
                                only_keys = missing
                            else:
                                only_keys = set()  # nothing to do
            except Exception as e:
                logger.debug(f"existing_key_checker failed, proceeding without filter: {e}")
                only_keys = None

            # Parse COG headers for filtered assets (when only_keys is set)
            cog_records = []
            if only_keys is not None and len(only_keys) == 0:
                # Nothing to do for this item
                logger.debug(f"Skipping item {getattr(stac_item, 'id', 'unknown')} - all cog_keys already ingested")
            else:
                # Backward-compatible call: some tests may monkeypatch this method without the new only_keys param
                try:
                    cog_records = await self.cog_processor.parse_cog_headers_for_item(
                        stac_item, max_concurrent_requests, only_keys=only_keys
                    )
                except TypeError:
                    # Fallback to legacy signature
                    cog_records = await self.cog_processor.parse_cog_headers_for_item(
                        stac_item, max_concurrent_requests
                    )

            # Compute base scene record ONCE per item to avoid repeated heavy flattening
            try:
                item_dict = stac_item.to_dict() if hasattr(stac_item, "to_dict") else stac_item
                base_scene = self.unified_schema.flatten_stac_item(item_dict)
            except Exception as e:
                logger.error(f"Error flattening STAC item {getattr(stac_item, 'id', 'unknown')}: {e}")
                base_scene = None

            # Create unified records (one per COG asset)
            for cog_record in cog_records:
                try:
                    if base_scene is None:
                        # Fallback to original create_unified_record path if flatten failed
                        unified_record = self.unified_schema.create_unified_record(
                            stac_item=stac_item, cog_metadata=cog_record
                        )
                    else:
                        # Map COG fields into unified schema names and merge with base_scene
                        cog = {
                            "cog_key": cog_record.get("asset_key"),
                            "cog_href": cog_record.get("asset_href"),
                            "cog_title": cog_record.get("asset_title"),
                            "cog_roles": cog_record.get("asset_roles", []),
                            "cog_width": cog_record.get("cog_width"),
                            "cog_height": cog_record.get("cog_height"),
                            "cog_tile_width": cog_record.get("cog_tile_width"),
                            "cog_tile_height": cog_record.get("cog_tile_height"),
                            "cog_dtype": cog_record.get("cog_dtype"),
                            "cog_compression": cog_record.get("cog_compression"),
                            "cog_predictor": cog_record.get("cog_predictor"),
                            "cog_crs": cog_record.get("cog_crs"),
                            "cog_transform": cog_record.get("cog_transform"),
                            "cog_tile_offsets": cog_record.get("cog_tile_offsets"),
                            "cog_tile_byte_counts": cog_record.get("cog_tile_byte_counts"),
                            "cog_scale": cog_record.get("cog_scale") or cog_record.get("scale"),
                            "cog_offset": cog_record.get("cog_offset") or cog_record.get("offset"),
                        }
                        unified_record = {**base_scene, **cog}
                    unified_records.append(unified_record)
                except Exception as e:
                    logger.error(
                        f"Error creating unified record for {getattr(stac_item, 'id', 'unknown')}, asset {cog_record.get('asset_key', 'unknown')}: {e}"
                    )
                    continue

            logger.debug(
                f"Created {len(unified_records)} unified records for STAC item {getattr(stac_item, 'id', 'unknown')}"
            )

        except Exception as e:
            logger.error(f"Error creating unified records for item {stac_item.id}: {e}")

        return unified_records

    def extract_scale_offset_from_asset(self, asset) -> Tuple[float, float]:
        """
        Extract scale and offset from asset raster:bands extension.

        Args:
            asset: STAC asset object

        Returns:
            Tuple of (scale, offset) or (None, None) if not found
        """
        try:
            raster_bands = asset.extra_fields.get("raster:bands", [])
            if (
                raster_bands
                and isinstance(raster_bands, list)
                and len(raster_bands) > 0
            ):
                first_band = raster_bands[0]
                scale = first_band.get("scale", None)
                offset = first_band.get("offset", None)

                if scale is not None or offset is not None:
                    logger.debug(f"Found scale={scale}, offset={offset} for asset")
                    return scale, offset
        except Exception as e:
            logger.debug(f"Error extracting scale/offset from asset: {e}")

        return None, None

    def is_cog_asset(self, asset) -> bool:
        """
        Check if asset is a COG asset.

        Args:
            asset: STAC asset object

        Returns:
            True if asset is a COG, False otherwise
        """
        if not asset.href:
            return False

        # Check media type
        if asset.media_type in [
            "image/tiff",
            "image/tiff; application=geotiff",
            "image/tiff; profile=cloud-optimized",
        ]:
            return True

        # Check for COG role
        if asset.roles and "data" in asset.roles:
            return True

        # Check file extension as fallback
        if asset.href.lower().endswith((".tif", ".tiff")):
            return True

        return False
