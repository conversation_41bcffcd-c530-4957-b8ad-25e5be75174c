# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Enhanced STAC indexer with Delta Lake and spatial indexing support."""

import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
import asyncio
import pyarrow as pa
import pyarrow.compute as pc
from deltalake import write_deltalake
from pystac import Item
import httpx
from shapely.geometry import shape as shapely_shape

from data_marketplace.config.settings import Settings
from data_marketplace.storage.delta_manager import DeltaManager
from data_marketplace.storage.master_index import MasterIndexManager
from data_marketplace.spatial.s2_utils import S2Utils
from data_marketplace.spatial.bbox_utils import BboxUtils, BboxStruct
from data_marketplace.ingestion.bloom_config import BloomFilterConfig

logger = logging.getLogger(__name__)


class DeltaStacIndexer:
    """Enhanced STAC indexer with Delta Lake storage and spatial indexing."""
    
    def __init__(self, settings: Optional[Settings] = None):
        """
        Initialize Delta STAC indexer.
        
        Args:
            settings: Settings instance (uses global if None)
        """
        if settings is None:
            from data_marketplace.config.settings import settings as global_settings
            settings = global_settings
        
        self.settings = settings
        self.delta_manager = DeltaManager(settings)
        self.master_index = MasterIndexManager(settings)
        self.s2_utils = S2Utils(cell_level=settings.spatial.s2_cell_level)
        self.bbox_utils = BboxUtils(precision=settings.spatial.bbox_precision)
        self.bloom_config = BloomFilterConfig(settings)
        self.logger = logger
    
    async def build_index(
        self,
        collection_name: str,
        bbox: Optional[Tuple[float, float, float, float]] = None,
        date_range: Optional[Tuple[str, str]] = None,
        **kwargs
    ) -> bool:
        """
        Build Delta Lake index for a STAC collection.
        
        Args:
            collection_name: Name of the collection
            bbox: Bounding box filter (minx, miny, maxx, maxy)
            date_range: Date range filter (start, end)
            **kwargs: Additional parameters
            
        Returns:
            True if successful
        """
        self.logger.info(f"Building Delta Lake index for collection: {collection_name}")
        
        try:
            # Create master index if it doesn't exist
            if not self.master_index.create_master_index():
                self.logger.error("Failed to create master index")
                return False
            
            # TODO: Implement actual STAC item fetching and processing
            # This is a placeholder for the enhanced indexing logic
            
            self.logger.info(f"Successfully built index for {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to build index for {collection_name}: {e}")
            return False
    
    def process_stac_items(
        self,
        stac_items: List[Dict[str, Any]],
        collection_name: str
    ) -> bool:
        """
        Process STAC items and store in Delta Lake.
        
        Args:
            stac_items: List of STAC item dictionaries
            collection_name: Name of the collection
            
        Returns:
            True if successful
        """
        if not stac_items:
            return True
        
        try:
            # Convert STAC items to enhanced format
            processed_items = []
            master_index_records = []
            
            for item in stac_items:
                processed_item, master_record = self._process_single_item(
                    item, collection_name
                )
                processed_items.append(processed_item)
                master_index_records.append(master_record)
            
            # Create collection table if it doesn't exist
            if not self._ensure_collection_table(collection_name, processed_items[0]):
                return False
            
            # Write to collection table
            collection_table = pa.table(processed_items)
            success = self.delta_manager.write_table(
                table_name=collection_name,
                data=collection_table,
                mode="append",
                partition_by=["year", "month"]
            )
            
            if not success:
                return False
            
            # Update master index
            return self.master_index.add_scenes(master_index_records)
            
        except Exception as e:
            self.logger.error(f"Failed to process STAC items: {e}")
            return False
    
    def _process_single_item(
        self, 
        stac_item: Dict[str, Any], 
        collection_name: str
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Process a single STAC item for enhanced storage.
        
        Args:
            stac_item: STAC item dictionary
            collection_name: Collection name
            
        Returns:
            Tuple of (processed_item, master_index_record)
        """
        # Extract basic properties
        scene_id = stac_item["id"]
        geometry = stac_item["geometry"]
        properties = stac_item["properties"]
        bbox_list = stac_item["bbox"]
        
        # Convert datetime
        datetime_str = properties.get("datetime")
        if datetime_str:
            dt = datetime.fromisoformat(datetime_str.replace("Z", "+00:00"))
        else:
            dt = datetime.now()
        
        # Create enhanced bbox
        bbox_struct = self.bbox_utils.list_to_bbox_struct(bbox_list)
        
        # Generate S2 cell ID
        s2_cell_id = self.s2_utils.bbox_to_s2_cells(bbox_struct.to_tuple())[0]
        
        # Create processed item
        processed_item = {
            "scene_id": scene_id,
            "collection": collection_name,
            "datetime": dt,
            "year": dt.year,
            "month": dt.month,
            "s2_cell_id": s2_cell_id,
            "bbox": bbox_struct.to_dict(),
            "geometry": geometry,  # Keep as GeoJSON for now
            "properties": properties,
            "assets": stac_item.get("assets", {}),
            "links": stac_item.get("links", []),
        }
        
        # Create master index record
        master_record = {
            "scene_id": scene_id,
            "collection": collection_name,
            "datetime": dt,
            "year": dt.year,
            "month": dt.month,
            "s2_cell_id": s2_cell_id,
            "bbox": bbox_struct.to_dict(),
            "geometry": self._geometry_to_wkb(geometry),
            "table_path": self.delta_manager.get_table_path(collection_name),
            "partition_path": f"year={dt.year}/month={dt.month}",
            "file_size": 0,  # TODO: Calculate actual file size
            "created_at": datetime.now(),
        }
        
        return processed_item, master_record
    
    def _ensure_collection_table(
        self, 
        collection_name: str, 
        sample_item: Dict[str, Any]
    ) -> bool:
        """
        Ensure collection table exists with proper schema.
        
        Args:
            collection_name: Name of the collection
            sample_item: Sample item to infer schema
            
        Returns:
            True if successful
        """
        if self.delta_manager.table_exists(collection_name):
            return True
        
        # Create schema from sample item
        schema = self._create_collection_schema()
        
        return self.delta_manager.create_table(
            table_name=collection_name,
            schema=schema,
            partition_by=["year", "month"]
        )
    
    def _create_collection_schema(self) -> pa.Schema:
        """Create PyArrow schema for collection tables."""
        return pa.schema([
            ("scene_id", pa.string()),
            ("collection", pa.string()),
            ("datetime", pa.timestamp("us")),
            ("year", pa.int32()),
            ("month", pa.int32()),
            ("s2_cell_id", pa.uint64()),
            ("bbox", self.bbox_utils.get_bbox_schema()),
            ("geometry", pa.string()),  # GeoJSON as string
            ("properties", pa.string()),  # JSON as string
            ("assets", pa.string()),  # JSON as string
            ("links", pa.string()),  # JSON as string
        ])
    
    def _geometry_to_wkb(self, geometry: Dict[str, Any]) -> bytes:
        """
        Convert GeoJSON geometry to WKB format.
        
        Args:
            geometry: GeoJSON geometry dictionary
            
        Returns:
            WKB bytes
        """
        # TODO: Implement actual GeoJSON to WKB conversion
        # For now, return empty bytes
        return b""

    def ensure_table_schema(
        self,
        input_table: pa.Table,
        target_schema: pa.Schema,
        table_description: str = "Input Table"
    ) -> pa.Table:
        """
        Creates a new table by casting columns from the input table to match the
        target schema precisely, including nullability and types.

        Reused from existing rasteret implementation for consistency.
        """
        self.logger.debug(f"--- Ensuring Schema for {table_description} ---")
        if input_table.schema.equals(target_schema):
            self.logger.debug(f"Schema for {table_description} already matches target.")
            return input_table

        self.logger.info(f"Rebuilding table for {table_description} to match target schema.")
        self.logger.debug(f"Source Schema (Inferred):\n{input_table.schema}")
        self.logger.debug(f"Target Schema:\n{target_schema}")

        corrected_arrays = []
        for field in target_schema:
            col_name = field.name
            target_type = field.type
            self.logger.debug(f"Processing column '{col_name}' -> Target type: {target_type}")
            try:
                # Get the column from the input table created by from_pylist
                current_column = input_table.column(col_name)
                if not current_column.type.equals(target_type):
                    self.logger.info(f"Casting '{col_name}' from {current_column.type} to {target_type}")
                    # Perform the cast - this should handle compatible types
                    # and fixed/variable list differences if data is valid.
                    # Cast also typically handles nullability based on target type.
                    casted_column = current_column.cast(target_type)
                    corrected_arrays.append(casted_column)
                else:
                    # Type already matches, append as is
                    corrected_arrays.append(current_column)
            except KeyError:
                self.logger.error(f"Column '{col_name}' missing in source table derived from pylist.")
                raise ValueError(f"Cannot create table: Column '{col_name}' missing.")
            except (pa.ArrowInvalid, pa.ArrowTypeError, TypeError, ValueError) as e:
                self.logger.error(f"Failed to cast column '{col_name}' to type {target_type}: {e}", exc_info=True)
                raise ValueError(f"Failed casting column '{col_name}'") from e
            except Exception as e:
                self.logger.error(f"Unexpected error processing column '{col_name}': {e}", exc_info=True)
                raise e

        # Create the new table directly with the corrected arrays and the target schema
        try:
            corrected_table = pa.Table.from_arrays(corrected_arrays, schema=target_schema)
            self.logger.debug(f"Successfully created table with target schema for {table_description}")
            return corrected_table
        except Exception as e:
            self.logger.error(f"Failed to create table with target schema: {e}", exc_info=True)
            raise ValueError(f"Failed to create corrected table for {table_description}") from e

    async def process_stac_items_enhanced(
        self,
        items: List[Item],
        collection_id: str,
        target_schema: pa.Schema,
        partition_cols: List[str],
        batch_size: int = 50,
        http_client: Optional[httpx.AsyncClient] = None
    ) -> Dict[str, Any]:
        """
        Enhanced STAC item processing with spatial indexing and optimal batching.

        Args:
            items: List of STAC items to process
            collection_id: Collection identifier
            target_schema: Target PyArrow schema for the table
            partition_cols: Columns to partition by (e.g., ["year", "month"])
            batch_size: Number of items to process in each batch
            http_client: HTTP client for COG header parsing

        Returns:
            Processing statistics
        """
        self.logger.info(f"Processing {len(items)} STAC items for collection {collection_id}")

        total_processed = 0
        total_batches = 0

        # Process items in batches
        for i in range(0, len(items), batch_size):
            batch_items = items[i:i + batch_size]
            batch_num = total_batches + 1

            self.logger.info(f"Processing batch {batch_num} with {len(batch_items)} items")

            # Convert STAC items to records with spatial indexing
            batch_records = []
            for item in batch_items:
                try:
                    # Extract basic item information
                    record = {
                        "scene_id": item.id,
                        "collection": collection_id,
                        "datetime": item.datetime.isoformat() if item.datetime else None,
                        "geometry": item.geometry,
                    }

                    # Add spatial indexing
                    if item.geometry:
                        # Convert to bbox struct
                        bbox_tuple = item.bbox if item.bbox else self._extract_bbox_from_geometry(item.geometry)
                        if bbox_tuple:
                            bbox_struct = self.bbox_utils.tuple_to_struct(bbox_tuple)
                            record["bbox"] = bbox_struct

                            # Compute S2 cell ID
                            s2_cell_id = self.s2_utils.bbox_to_s2_cells(bbox_tuple, max_cells=1)
                            if s2_cell_id:
                                record["s2_cell_id"] = s2_cell_id[0]

                    # Add temporal partitioning fields
                    if item.datetime:
                        record["year"] = item.datetime.year
                        record["month"] = item.datetime.month

                    batch_records.append(record)

                except Exception as e:
                    self.logger.warning(f"Failed to process item {item.id}: {e}")
                    continue

            if not batch_records:
                self.logger.warning(f"No valid records in batch {batch_num}")
                continue

            # Sort records by S2 cell ID for spatial locality (Hilbert curve ordering)
            batch_records = self.s2_utils.hilbert_sort_by_s2(batch_records)

            # Convert to PyArrow table
            batch_table = pa.Table.from_pylist(batch_records)

            # Ensure schema compliance
            batch_table = self.ensure_table_schema(batch_table, target_schema, f"Batch {batch_num}")

            # Write to Delta Lake with optimized settings
            write_args = {
                "table_or_uri": self.settings.delta_lake.table_root + f"/{collection_id}",
                "data": batch_table,
                "partition_by": partition_cols,
                "mode": "append",
                "schema_mode": "merge",  # Allow schema evolution
                "engine": "rust",  # Use Rust engine for better performance
            }

            # Add storage options for S3
            if self.settings.s3.bucket_name:
                write_args["storage_options"] = {
                    "AWS_REGION": self.settings.s3.region,
                    "AWS_S3_ALLOW_UNSAFE_RENAME": "true",
                }
                if self.settings.s3.access_key_id:
                    write_args["storage_options"]["AWS_ACCESS_KEY_ID"] = self.settings.s3.access_key_id
                if self.settings.s3.secret_access_key:
                    write_args["storage_options"]["AWS_SECRET_ACCESS_KEY"] = self.settings.s3.secret_access_key

            # Write batch to Delta Lake
            write_deltalake(**write_args)

            total_processed += len(batch_records)
            total_batches += 1

            self.logger.info(f"Successfully wrote batch {batch_num} with {len(batch_records)} records")

        return {
            "total_items": len(items),
            "total_processed": total_processed,
            "total_batches": total_batches,
            "collection_id": collection_id,
            "status": "completed"
        }

    def _extract_bbox_from_geometry(self, geometry: Dict[str, Any]) -> Optional[Tuple[float, float, float, float]]:
        """Extract bbox from GeoJSON geometry."""
        try:
            geom = shapely_shape(geometry)
            bounds = geom.bounds
            return (bounds[0], bounds[1], bounds[2], bounds[3])  # minx, miny, maxx, maxy
        except Exception as e:
            self.logger.warning(f"Failed to extract bbox from geometry: {e}")
            return None
