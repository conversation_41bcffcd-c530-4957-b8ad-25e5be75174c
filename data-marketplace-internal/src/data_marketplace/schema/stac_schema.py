# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Optimized STAC schema for rasteret-style queries with GeoArrow support.
Flattened structure for maximum query performance.
"""

import pyarrow as pa
import geoarrow.pyarrow as ga
from typing import Dict, Any


def get_optimized_stac_schema() -> pa.Schema:
    """
    Get the optimized STAC schema for rasteret queries.
    
    This schema is flattened and denormalized for maximum query performance:
    - Asset URLs brought to top level
    - Common properties flattened
    - Spatial indexing fields added
    - COG metadata included
    
    Returns:
        PyArrow schema optimized for analytical queries
    """
    return pa.schema([
        # Core identification
        ("scene_id", pa.string()),
        ("collection", pa.string()),
        
        # Temporal fields (for partitioning and filtering)
        ("datetime", pa.timestamp("us", tz="UTC")),
        ("year", pa.int16()),
        ("month", pa.int8()),
        
        # Spatial fields - GeoArrow 1.2 compliant
        ("bbox", pa.struct([
            ("xmin", pa.float64()),
            ("ymin", pa.float64()),
            ("xmax", pa.float64()),
            ("ymax", pa.float64())
        ])),
        ("geometry", ga.wkb()),  # GeoArrow WKB encoding for compatibility and optimal Parquet statistics
        ("s2_cell_id", pa.string()),  # S2 cell ID for spatial indexing (string for Delta Lake compatibility)
        
        # Asset URLs (flattened for direct access)
        ("visual_href", pa.string()),      # Primary visual asset
        ("thumbnail_href", pa.string()),   # Thumbnail for preview
        ("metadata_href", pa.string()),    # Metadata file
        ("overview_href", pa.string()),    # Overview/reduced resolution
        
        # COG metadata (from header parsing)
        ("cog_resolution", pa.float32()),  # Spatial resolution in meters
        ("cog_width", pa.int32()),         # Image width in pixels
        ("cog_height", pa.int32()),        # Image height in pixels
        ("cog_bands", pa.int8()),          # Number of bands
        ("cog_data_type", pa.string()),    # Data type (uint8, uint16, float32, etc.)
        ("cog_compression", pa.string()),  # Compression type
        ("cog_overview_count", pa.int8()), # Number of overview levels
        ("cog_file_size", pa.int64()),     # File size in bytes
        
        # Common STAC properties (flattened for fast filtering)
        ("cloud_cover", pa.float32()),     # Cloud coverage percentage
        ("sun_elevation", pa.float32()),   # Sun elevation angle
        ("sun_azimuth", pa.float32()),     # Sun azimuth angle
        ("view_angle", pa.float32()),      # Viewing angle
        
        # Platform/instrument info
        ("platform", pa.string()),        # Satellite platform
        ("instrument", pa.string()),      # Instrument name
        ("constellation", pa.string()),   # Constellation name
        
        # Processing info
        ("processing_level", pa.string()), # Processing level (L1C, L2A, etc.)
        ("product_type", pa.string()),     # Product type
        
        # Quality indicators
        ("quality_flag", pa.string()),     # Quality assessment
        ("data_coverage", pa.float32()),   # Data coverage percentage
        
        # Additional metadata (JSON for flexibility)
        ("additional_properties", pa.string()),  # JSON string for other properties
        
        # Ingestion metadata
        ("ingested_at", pa.timestamp("us", tz="UTC")),
        ("source_catalog", pa.string()),   # Which STAC catalog this came from
        ("last_updated", pa.timestamp("us", tz="UTC")),
    ])


def get_bulk_ingestion_schema() -> pa.Schema:
    """
    Get schema optimized for bulk ingestion with larger batches.
    Same fields as optimized schema but with different nullability.
    """
    schema = get_optimized_stac_schema()
    
    # Make some fields nullable for bulk ingestion flexibility
    nullable_fields = [
        "thumbnail_href", "metadata_href", "overview_href",
        "cloud_cover", "sun_elevation", "sun_azimuth", "view_angle",
        "quality_flag", "data_coverage", "additional_properties"
    ]
    
    new_fields = []
    for field in schema:
        if field.name in nullable_fields:
            new_fields.append(pa.field(field.name, field.type, nullable=True))
        else:
            new_fields.append(field)
    
    return pa.schema(new_fields)


def get_native_geoarrow_stac_schema() -> pa.Schema:
    """
    Get STAC schema with native GeoArrow geometry encoding.

    This schema uses native GeoArrow types instead of WKB for better
    performance with simple geometries and improved compression.

    Returns:
        PyArrow schema with native GeoArrow geometry types
    """
    fields = [
        # Core STAC fields
        ("scene_id", pa.string()),
        ("collection", pa.string()),
        ("datetime", pa.timestamp("us", tz="UTC")),
        ("year", pa.int16()),
        ("month", pa.int8()),

        # Spatial fields - Native GeoArrow encoding
        ("bbox", pa.struct([
            ("xmin", pa.float64()),
            ("ymin", pa.float64()),
            ("xmax", pa.float64()),
            ("ymax", pa.float64())
        ])),
        ("geometry", ga.multipolygon()),  # Native GeoArrow for most STAC geometries
        ("s2_cell_id", pa.string()),  # S2 cell ID for spatial indexing (string for Delta Lake compatibility)

        # Asset URLs (flattened for direct access)
        ("visual_href", pa.string()),      # Primary visual asset
        ("thumbnail_href", pa.string()),   # Thumbnail for preview
        ("metadata_href", pa.string()),    # Metadata file
        ("overview_href", pa.string()),    # Overview/reduced resolution

        # COG metadata (extracted from headers)
        ("width", pa.int32()),
        ("height", pa.int32()),
        ("bands", pa.int16()),
        ("dtype", pa.string()),
        ("nodata", pa.float64()),
        ("crs", pa.string()),
        ("transform", pa.list_(pa.float64())),  # Affine transform as array

        # Common STAC properties (flattened)
        ("cloud_cover", pa.float32()),
        ("sun_elevation", pa.float32()),
        ("sun_azimuth", pa.float32()),
        ("view_angle", pa.float32()),
        ("platform", pa.string()),
        ("instrument", pa.string()),
        ("constellation", pa.string()),
        ("processing_level", pa.string()),
        ("product_type", pa.string()),

        # Ingestion metadata
        ("ingested_at", pa.timestamp("us", tz="UTC")),
        ("source_catalog", pa.string()),
        ("last_updated", pa.timestamp("us", tz="UTC")),
    ]

    return pa.schema(fields)


def get_adaptive_geoarrow_schema(geometry_type: str = "auto") -> pa.Schema:
    """
    Get STAC schema with adaptive GeoArrow geometry encoding.

    Args:
        geometry_type: Target geometry type ("point", "polygon", "multipolygon", "auto")

    Returns:
        PyArrow schema with appropriate GeoArrow geometry type
    """
    # Base fields (same as optimized schema)
    fields = [
        # Core STAC fields
        ("scene_id", pa.string()),
        ("collection", pa.string()),
        ("datetime", pa.timestamp("us", tz="UTC")),
        ("year", pa.int16()),
        ("month", pa.int8()),

        # Spatial fields with adaptive geometry type
        ("bbox", pa.struct([
            ("xmin", pa.float64()),
            ("ymin", pa.float64()),
            ("xmax", pa.float64()),
            ("ymax", pa.float64())
        ])),
    ]

    # Choose geometry type based on parameter
    if geometry_type == "point":
        geometry_field = ("geometry", ga.point())
    elif geometry_type == "polygon":
        geometry_field = ("geometry", ga.polygon())
    elif geometry_type == "multipolygon":
        geometry_field = ("geometry", ga.multipolygon())
    else:
        # Auto/default - use WKB for maximum compatibility
        geometry_field = ("geometry", ga.wkb())

    fields.append(geometry_field)
    fields.append(("s2_cell_id", pa.uint64()))

    # Add remaining fields (same as optimized schema)
    remaining_fields = [
        # Asset URLs
        ("visual_href", pa.string()),
        ("thumbnail_href", pa.string()),
        ("metadata_href", pa.string()),
        ("overview_href", pa.string()),

        # COG metadata
        ("width", pa.int32()),
        ("height", pa.int32()),
        ("bands", pa.int16()),
        ("dtype", pa.string()),
        ("nodata", pa.float64()),
        ("crs", pa.string()),
        ("transform", pa.list_(pa.float64())),

        # STAC properties
        ("cloud_cover", pa.float32()),
        ("sun_elevation", pa.float32()),
        ("sun_azimuth", pa.float32()),
        ("view_angle", pa.float32()),
        ("platform", pa.string()),
        ("instrument", pa.string()),
        ("constellation", pa.string()),
        ("processing_level", pa.string()),
        ("product_type", pa.string()),

        # Ingestion metadata
        ("ingested_at", pa.timestamp("us", tz="UTC")),
        ("source_catalog", pa.string()),
        ("last_updated", pa.timestamp("us", tz="UTC")),
    ]

    fields.extend(remaining_fields)
    return pa.schema(fields)


def get_partition_columns() -> list[str]:
    """Get the recommended partition columns for the STAC table."""
    return ["year", "month"]


def get_sort_columns() -> list[str]:
    """Get the recommended sort columns for spatial locality."""
    return ["s2_cell_id", "datetime"]


def get_bloom_filter_columns() -> list[str]:
    """Get columns that should have Bloom filters for fast lookups."""
    return [
        "scene_id",        # Unique scene lookups
        "collection",      # Collection filtering
        "s2_cell_id",      # Spatial range queries
        "visual_href",     # Asset URL lookups
        "platform",       # Platform filtering
        "processing_level" # Processing level filtering
    ]


# Schema validation helpers
def validate_stac_record(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and clean a STAC record for ingestion.
    
    Args:
        record: Raw STAC record dictionary
        
    Returns:
        Cleaned record ready for PyArrow table creation
    """
    # Ensure required fields are present
    required_fields = ["scene_id", "collection", "datetime"]
    for field in required_fields:
        if field not in record or record[field] is None:
            raise ValueError(f"Required field '{field}' is missing or null")
    
    # Clean and validate data types
    cleaned = record.copy()
    
    # Ensure numeric fields are proper types
    numeric_fields = {
        "year": int,
        "month": int,
        "cog_resolution": float,
        "cog_width": int,
        "cog_height": int,
        "cog_bands": int,
        "cloud_cover": float,
        "sun_elevation": float,
        "sun_azimuth": float,
        "view_angle": float,
        "data_coverage": float,
    }
    
    for field, expected_type in numeric_fields.items():
        if field in cleaned and cleaned[field] is not None:
            try:
                cleaned[field] = expected_type(cleaned[field])
            except (ValueError, TypeError):
                cleaned[field] = None  # Set to null if conversion fails
    
    return cleaned
